#!/usr/bin/env python3
"""
ABox Voice Changer - Unlock Screen Functionality Test Script

This script helps verify the unlock screen functionality by:
1. Checking if the app is installed
2. Clearing app data to simulate fresh install
3. Launching the app and checking for unlock screen
4. Testing unlock functionality with correct/incorrect codes

Requirements:
- Android device connected via ADB
- ADB in system PATH
- Python 3.6+

Usage:
    python unlock_test_script.py
"""

import subprocess
import time
import sys
import os

# App package name
PACKAGE_NAME = "com.abox.apps"
UNLOCK_CODE = "LOCK-5960"

class UnlockTester:
    def __init__(self):
        self.device_connected = False
        self.app_installed = False
        
    def run_adb_command(self, command):
        """Run ADB command and return output"""
        try:
            result = subprocess.run(
                f"adb {command}", 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return False, "", "Command timed out"
        except Exception as e:
            return False, "", str(e)
    
    def check_device_connection(self):
        """Check if Android device is connected"""
        print("🔍 Checking device connection...")
        success, output, error = self.run_adb_command("devices")
        
        if not success:
            print(f"❌ ADB not found or error: {error}")
            return False
            
        devices = [line for line in output.split('\n') if '\tdevice' in line]
        if not devices:
            print("❌ No Android device connected")
            print("   Please connect your Android device and enable USB debugging")
            return False
            
        print(f"✅ Device connected: {devices[0].split()[0]}")
        self.device_connected = True
        return True
    
    def check_app_installation(self):
        """Check if the app is installed"""
        print("🔍 Checking app installation...")
        success, output, error = self.run_adb_command(f"shell pm list packages {PACKAGE_NAME}")
        
        if success and PACKAGE_NAME in output:
            print(f"✅ App is installed: {PACKAGE_NAME}")
            self.app_installed = True
            return True
        else:
            print(f"❌ App not installed: {PACKAGE_NAME}")
            print("   Please install the APK first")
            return False
    
    def clear_app_data(self):
        """Clear app data to simulate fresh install"""
        print("🧹 Clearing app data...")
        success, output, error = self.run_adb_command(f"shell pm clear {PACKAGE_NAME}")
        
        if success:
            print("✅ App data cleared successfully")
            return True
        else:
            print(f"❌ Failed to clear app data: {error}")
            return False
    
    def launch_app(self):
        """Launch the app"""
        print("🚀 Launching app...")
        success, output, error = self.run_adb_command(
            f"shell am start -n {PACKAGE_NAME}/.activitys.SplashActivity"
        )
        
        if success:
            print("✅ App launched successfully")
            time.sleep(3)  # Wait for app to load
            return True
        else:
            print(f"❌ Failed to launch app: {error}")
            return False
    
    def check_current_activity(self):
        """Check current activity"""
        print("🔍 Checking current activity...")
        success, output, error = self.run_adb_command(
            "shell dumpsys activity activities | grep mResumedActivity"
        )
        
        if success and output:
            print(f"📱 Current activity: {output}")
            return "UnlockActivity" in output
        else:
            print("❓ Could not determine current activity")
            return False
    
    def input_text(self, text):
        """Input text to current focused field"""
        success, output, error = self.run_adb_command(f"shell input text '{text}'")
        return success
    
    def tap_unlock_button(self):
        """Tap the unlock button (approximate coordinates)"""
        # Note: These coordinates are approximate and may need adjustment
        # based on actual device screen size and layout
        success, output, error = self.run_adb_command("shell input tap 540 1200")
        return success
    
    def test_unlock_screen_appears(self):
        """Test if unlock screen appears on first launch"""
        print("\n🧪 TEST 1: Unlock screen appears on first launch")
        
        if not self.clear_app_data():
            return False
            
        if not self.launch_app():
            return False
            
        time.sleep(2)  # Wait for navigation
        
        if self.check_current_activity():
            print("✅ TEST 1 PASSED: Unlock screen appeared")
            return True
        else:
            print("❌ TEST 1 FAILED: Unlock screen did not appear")
            return False
    
    def test_wrong_unlock_code(self):
        """Test wrong unlock code"""
        print("\n🧪 TEST 2: Wrong unlock code shows error")
        
        print("   Entering wrong code...")
        if not self.input_text("wrong123"):
            print("❌ Failed to input text")
            return False
            
        time.sleep(1)
        
        print("   Tapping unlock button...")
        if not self.tap_unlock_button():
            print("❌ Failed to tap button")
            return False
            
        time.sleep(2)
        
        # Check if still on unlock screen (indicating error was shown)
        if self.check_current_activity():
            print("✅ TEST 2 PASSED: Still on unlock screen (error shown)")
            return True
        else:
            print("❌ TEST 2 FAILED: Left unlock screen unexpectedly")
            return False
    
    def test_correct_unlock_code(self):
        """Test correct unlock code"""
        print("\n🧪 TEST 3: Correct unlock code proceeds to main app")
        
        # Clear the field first
        self.run_adb_command("shell input keyevent KEYCODE_CTRL_A")
        time.sleep(0.5)
        
        print(f"   Entering correct code: {UNLOCK_CODE}")
        if not self.input_text(UNLOCK_CODE):
            print("❌ Failed to input text")
            return False
            
        time.sleep(1)
        
        print("   Tapping unlock button...")
        if not self.tap_unlock_button():
            print("❌ Failed to tap button")
            return False
            
        time.sleep(3)  # Wait for navigation
        
        # Check if we left the unlock screen
        if not self.check_current_activity():
            print("✅ TEST 3 PASSED: Left unlock screen (proceeded to main app)")
            return True
        else:
            print("❌ TEST 3 FAILED: Still on unlock screen")
            return False
    
    def test_persistence(self):
        """Test that unlock status persists"""
        print("\n🧪 TEST 4: Unlock status persists across app restarts")
        
        # Force stop the app
        print("   Stopping app...")
        self.run_adb_command(f"shell am force-stop {PACKAGE_NAME}")
        time.sleep(2)
        
        # Launch again
        if not self.launch_app():
            return False
            
        time.sleep(2)
        
        # Check if unlock screen is skipped
        if not self.check_current_activity():
            print("✅ TEST 4 PASSED: Unlock screen skipped (status persisted)")
            return True
        else:
            print("❌ TEST 4 FAILED: Unlock screen appeared again")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🎯 Starting ABox Voice Changer Unlock Screen Tests")
        print("=" * 60)
        
        # Prerequisites
        if not self.check_device_connection():
            return False
            
        if not self.check_app_installation():
            return False
        
        # Run tests
        tests = [
            self.test_unlock_screen_appears,
            self.test_wrong_unlock_code,
            self.test_correct_unlock_code,
            self.test_persistence
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print(f"🏁 TEST SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Unlock functionality is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")
        
        return passed == total

def main():
    """Main function"""
    print("ABox Voice Changer - Unlock Screen Test Script")
    print("=" * 50)
    
    tester = UnlockTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
