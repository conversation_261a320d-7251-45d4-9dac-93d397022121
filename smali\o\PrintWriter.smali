.class public final Lo/PrintWriter;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo/PrintWriter$Application;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\n\n\u0002\u0010\t\n\u0002\u0008\u0005\u0008\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001.B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0011J\u000e\u0010\u0019\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0011J\u0006\u0010\u001a\u001a\u00020\u0017J\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u000eJ\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u000eJ\u0006\u0010\u001d\u001a\u00020\u0004J\u0006\u0010\u001e\u001a\u00020\u001fJ\u0006\u0010 \u001a\u00020\u001fJ\u0006\u0010!\u001a\u00020\u001fJ\u0012\u0010\"\u001a\u00020\u001f2\n\u0008\u0002\u0010#\u001a\u0004\u0018\u00010\u0004J\u0006\u0010$\u001a\u00020\u001fJ\u0006\u0010%\u001a\u00020\u0017J\u000e\u0010&\u001a\u00020\u00172\u0006\u0010\'\u001a\u00020\u0006J\u000e\u0010(\u001a\u00020\u00172\u0006\u0010)\u001a\u00020*J\u000e\u0010+\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0011J\u0010\u0010,\u001a\u00020\u00172\u0008\u0010-\u001a\u0004\u0018\u00010\u000eR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0008\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000c\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u000f\u001a\u0012\u0012\u0004\u0012\u00020\u00110\u0010j\u0008\u0012\u0004\u0012\u00020\u0011`\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0013\u001a\n \u0015*\u0004\u0018\u00010\u00140\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"
    }
    d2 = {
        "Lcom/abox/apps/utils/AccountManager;",
        "",
        "()V",
        "ACCOUNT_INFO",
        "",
        "FROM_GUIDE_VIEW",
        "",
        "FROM_OTHER_VIEW",
        "FROM_VIEW_KEY",
        "FROM_VIEW_TYPE",
        "LAST_SHOW_TIME",
        "TAG",
        "TIME_PERIOD",
        "accountInfoCache",
        "Lcom/abox/apps/model/UserInfo;",
        "listeners",
        "Ljava/util/ArrayList;",
        "Lcom/abox/apps/utils/AccountManager$OnAccountInfoChangedListener;",
        "Lkotlin/collections/ArrayList;",
        "mmkv",
        "Lcom/tencent/mmkv/MMKV;",
        "kotlin.jvm.PlatformType",
        "addOnAccountInfoChangedListener",
        "",
        "listener",
        "addOnStickyAccountInfoChangedListener",
        "clearOnAccountInfoChangedListener",
        "getAccountInfo",
        "getAccountWithCache",
        "getUserToken",
        "isForever",
        "",
        "isGpLogin",
        "isLogin",
        "isShowRecommendView",
        "packageName",
        "isVip",
        "logout",
        "putFromViewTarget",
        "from",
        "putLastShowRecommendTime",
        "time",
        "",
        "removeOnAccountInfoChangedListener",
        "setAccountInfo",
        "accountInfo",
        "OnAccountInfoChangedListener",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nAccountManager.kt\nKotlin\n*S Kotlin\n*F\n+ 1 AccountManager.kt\ncom/abox/apps/utils/AccountManager\n+ 2 GsonKtx.kt\ncom/abox/apps/utils/GsonKtxKt\n*L\n1#1,165:1\n65#2,2:166\n61#2,8:168\n*S KotlinDebug\n*F\n+ 1 AccountManager.kt\ncom/abox/apps/utils/AccountManager\n*L\n48#1:166,2\n48#1:168,8\n*E\n"
    }
.end annotation


# static fields
.field private static final IconCompatParcelizer:Ljava/lang/String; = "AccountTag"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

.field private static RemoteActionCompatParcelizer:I = 0x0

.field public static final asBinder:Ljava/lang/String; = "from_view_key"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final asInterface:I = 0x3ea

.field public static final getDefaultImpl:Ljava/lang/String; = "from_view_type"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final onConnected:Ljava/lang/String; = "last_show_dialog_time_key"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final onConnectionFailed:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lo/PrintWriter$Application;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final onTransact:I = 0x3e9

.field private static read:Lcom/abox/apps/model/UserInfo; = null
    .annotation build Lo/cbw;
    .end annotation
.end field

.field public static final setDefaultImpl:Lo/PrintWriter;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final write:Ljava/lang/String; = "account_info"
    .annotation build Lo/cbz;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lo/PrintWriter;

    invoke-direct {v0}, Lo/PrintWriter;-><init>()V

    sput-object v0, Lo/PrintWriter;->setDefaultImpl:Lo/PrintWriter;

    const-string v0, "account_info"

    const/4 v1, 0x2

    invoke-static {v0, v1}, Lcom/tencent/mmkv/MMKV;->mmkvWithID(Ljava/lang/String;I)Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    sput-object v0, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    sput-object v0, Lo/PrintWriter;->onConnectionFailed:Ljava/util/ArrayList;

    const v0, 0x5265c00

    sput v0, Lo/PrintWriter;->RemoteActionCompatParcelizer:I

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic setDefaultImpl(Lo/PrintWriter;Ljava/lang/String;ILjava/lang/Object;)Z
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    :cond_0
    invoke-virtual {p0, p1}, Lo/PrintWriter;->getDefaultImpl(Ljava/lang/String;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final IconCompatParcelizer()Z
    .locals 2

    invoke-virtual {p0}, Lo/PrintWriter;->onTransact()Lcom/abox/apps/model/UserInfo;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/abox/apps/model/UserInfo;->getToken()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    const/4 v1, 0x1

    if-eqz v0, :cond_2

    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    move-result v0

    if-nez v0, :cond_1

    goto :goto_1

    :cond_1
    const/4 v0, 0x0

    goto :goto_2

    :cond_2
    :goto_1
    move v0, v1

    :goto_2
    xor-int/2addr v0, v1

    return v0
.end method

.method public final RemoteActionCompatParcelizer()Z
    .locals 4

    invoke-static {}, Lo/Switch;->onTransact()Lo/Spinner;

    move-result-object v0

    const-string v1, "com.google"

    const-string v2, "com.android.vending"

    invoke-interface {v0, v1, v2}, Lo/Spinner;->setDefaultImpl(Ljava/lang/String;Ljava/lang/String;)[Landroid/accounts/Account;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lkotlin/collections/ArraysKt;->getOrNull([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/accounts/Account;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "AccountTag isGpLogin:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v3, 0x20

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    new-array v3, v1, [Ljava/lang/Object;

    invoke-static {v2, v3}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    if-eqz v0, :cond_0

    const/4 v1, 0x1

    :cond_0
    return v1
.end method

.method public final asBinder()Lcom/abox/apps/model/UserInfo;
    .locals 4
    .annotation build Lo/cbw;
    .end annotation

    sget-object v0, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    const/4 v1, 0x0

    const-string v2, "account_info"

    invoke-virtual {v0, v2, v1}, Lcom/tencent/mmkv/MMKV;->decodeString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    :try_start_0
    invoke-static {}, Lo/AbstractMethodError;->getDefaultImpl()Lcom/google/gson/Gson;

    move-result-object v2

    const-class v3, Lcom/abox/apps/model/UserInfo;

    invoke-virtual {v2, v0, v3}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    check-cast v0, Lcom/abox/apps/model/UserInfo;

    move-object v1, v0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    const-string v2, "null cannot be cast to non-null type com.abox.apps.model.UserInfo"

    invoke-direct {v0, v2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_1
    :goto_0
    return-object v1
.end method

.method public final asBinder(Lo/PrintWriter$Application;)V
    .locals 1
    .param p1    # Lo/PrintWriter$Application;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string v0, "listener"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lo/PrintWriter;->onConnectionFailed:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public final asInterface()V
    .locals 1

    sget-object v0, Lo/PrintWriter;->onConnectionFailed:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    return-void
.end method

.method public final asInterface(J)V
    .locals 2

    sget-object v0, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    const-string v1, "last_show_dialog_time_key"

    invoke-virtual {v0, v1, p1, p2}, Lcom/tencent/mmkv/MMKV;->putLong(Ljava/lang/String;J)Landroid/content/SharedPreferences$Editor;

    return-void
.end method

.method public final asInterface(Lcom/abox/apps/model/UserInfo;)V
    .locals 2
    .param p1    # Lcom/abox/apps/model/UserInfo;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    if-eqz p1, :cond_0

    invoke-static {p1}, Lo/AbstractMethodError;->asBinder(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    sput-object p1, Lo/PrintWriter;->read:Lcom/abox/apps/model/UserInfo;

    sget-object p1, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    const-string v1, "account_info"

    invoke-virtual {p1, v1, v0}, Lcom/tencent/mmkv/MMKV;->encode(Ljava/lang/String;Ljava/lang/String;)Z

    invoke-static {}, Lo/akk;->asBinder()Lo/akk;

    move-result-object p1

    invoke-virtual {p1}, Lo/akm;->asInterface()Lo/akq;

    move-result-object p1

    new-instance v0, Lo/akl;

    const-string v1, "action_refresh_user_info"

    invoke-direct {v0, v1}, Lo/akl;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1, v0}, Lo/akq;->setDefaultImpl(Lo/akl;)V

    :cond_0
    return-void
.end method

.method public final getDefaultImpl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    invoke-virtual {p0}, Lo/PrintWriter;->onTransact()Lcom/abox/apps/model/UserInfo;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/abox/apps/model/UserInfo;->getToken()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_1

    :cond_0
    const-string v0, ""

    :cond_1
    return-object v0
.end method

.method public final getDefaultImpl(Ljava/lang/String;)Z
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    sget-object v0, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    const-string v3, "last_show_dialog_time_key"

    invoke-virtual {v0, v3, v1, v2}, Lcom/tencent/mmkv/MMKV;->getLong(Ljava/lang/String;J)J

    move-result-wide v1

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    sub-long/2addr v3, v1

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "AccountTag period:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    new-array v5, v2, [Ljava/lang/Object;

    invoke-static {v1, v5}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    const-wide/16 v5, 0x5

    cmp-long v1, v5, v3

    const/4 v5, 0x1

    if-gtz v1, :cond_0

    sget v1, Lo/PrintWriter;->RemoteActionCompatParcelizer:I

    int-to-long v6, v1

    cmp-long v1, v3, v6

    if-gez v1, :cond_0

    move v1, v5

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    if-eqz v1, :cond_1

    return v2

    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "AccountTag isLogin:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Lo/PrintWriter;->IconCompatParcelizer()Z

    move-result v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v3, " isVip:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Lo/PrintWriter;->write()Z

    move-result v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-array v3, v2, [Ljava/lang/Object;

    invoke-static {v1, v3}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    invoke-virtual {p0}, Lo/PrintWriter;->IconCompatParcelizer()Z

    move-result v1

    if-nez v1, :cond_9

    invoke-virtual {p0}, Lo/PrintWriter;->write()Z

    move-result v1

    if-eqz v1, :cond_2

    goto/16 :goto_5

    :cond_2
    const/16 v1, 0x3ea

    const-string v3, "from_view_key"

    invoke-virtual {v0, v3, v1}, Lcom/tencent/mmkv/MMKV;->getInt(Ljava/lang/String;I)I

    move-result v0

    const/16 v1, 0x3e9

    if-ne v0, v1, :cond_3

    new-array p1, v2, [Ljava/lang/Object;

    const-string v0, "AccountTag without displaying pop-ups"

    invoke-static {v0, p1}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    return v2

    :cond_3
    new-array v0, v2, [Ljava/lang/Object;

    const-string v1, "AccountTag  show dialog"

    invoke-static {v1, v0}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    sget-object v0, Lo/LineNumberReader;->setDefaultImpl:Lo/LineNumberReader;

    invoke-virtual {v0}, Lo/LineNumberReader;->onTransact()Lcom/abox/apps/model/CommonConfig;

    move-result-object v0

    invoke-virtual {v0}, Lcom/abox/apps/model/CommonConfig;->getRequireGoogleLogin()Ljava/util/List;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "AccountTag packageList:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v3, " packageName:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-array v3, v2, [Ljava/lang/Object;

    invoke-static {v1, v3}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    if-eqz p1, :cond_5

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v1

    if-nez v1, :cond_4

    goto :goto_1

    :cond_4
    move v1, v2

    goto :goto_2

    :cond_5
    :goto_1
    move v1, v5

    :goto_2
    if-nez v1, :cond_8

    if-eqz v0, :cond_7

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_6

    goto :goto_3

    :cond_6
    move v1, v2

    goto :goto_4

    :cond_7
    :goto_3
    move v1, v5

    :goto_4
    if-nez v1, :cond_8

    invoke-interface {v0, p1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p1

    invoke-virtual {p0}, Lo/PrintWriter;->RemoteActionCompatParcelizer()Z

    move-result v0

    if-eqz p1, :cond_8

    if-nez v0, :cond_8

    return v2

    :cond_8
    new-array p1, v2, [Ljava/lang/Object;

    const-string v0, "AccountTag show dialog ...."

    invoke-static {v0, p1}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    return v5

    :cond_9
    :goto_5
    return v2
.end method

.method public final onTransact()Lcom/abox/apps/model/UserInfo;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    sget-object v0, Lo/PrintWriter;->read:Lcom/abox/apps/model/UserInfo;

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lo/PrintWriter;->asBinder()Lcom/abox/apps/model/UserInfo;

    move-result-object v0

    sput-object v0, Lo/PrintWriter;->read:Lcom/abox/apps/model/UserInfo;

    :cond_0
    sget-object v0, Lo/PrintWriter;->read:Lcom/abox/apps/model/UserInfo;

    return-object v0
.end method

.method public final onTransact(Lo/PrintWriter$Application;)V
    .locals 1
    .param p1    # Lo/PrintWriter$Application;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string v0, "listener"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, p1}, Lo/PrintWriter;->setDefaultImpl(Lo/PrintWriter$Application;)V

    invoke-virtual {p0}, Lo/PrintWriter;->onTransact()Lcom/abox/apps/model/UserInfo;

    move-result-object v0

    invoke-interface {p1, v0}, Lo/PrintWriter$Application;->getDefaultImpl(Lcom/abox/apps/model/UserInfo;)V

    return-void
.end method

.method public final read()V
    .locals 3

    const/4 v0, 0x0

    sput-object v0, Lo/PrintWriter;->read:Lcom/abox/apps/model/UserInfo;

    sget-object v0, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    const-string v1, "account_info"

    invoke-virtual {v0, v1}, Lcom/tencent/mmkv/MMKV;->remove(Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    invoke-static {}, Lo/akk;->asBinder()Lo/akk;

    move-result-object v0

    invoke-virtual {v0}, Lo/akm;->asInterface()Lo/akq;

    move-result-object v0

    new-instance v1, Lo/akl;

    const-string v2, "action_refresh_user_info"

    invoke-direct {v1, v2}, Lo/akl;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lo/akq;->setDefaultImpl(Lo/akl;)V

    new-instance v0, Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions$Builder;

    sget-object v1, Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions;->DEFAULT_SIGN_IN:Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions;

    invoke-direct {v0, v1}, Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions$Builder;-><init>(Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions;)V

    invoke-virtual {v0}, Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions$Builder;->requestEmail()Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions$Builder;

    move-result-object v0

    invoke-virtual {v0}, Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions$Builder;->build()Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions;

    move-result-object v0

    const-string v1, "build(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/google/android/gms/auth/api/signin/GoogleSignIn;->getClient(Landroid/content/Context;Lcom/google/android/gms/auth/api/signin/GoogleSignInOptions;)Lcom/google/android/gms/auth/api/signin/GoogleSignInClient;

    move-result-object v0

    const-string v1, "getClient(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0}, Lcom/google/android/gms/auth/api/signin/GoogleSignInClient;->signOut()Lcom/google/android/gms/tasks/Task;

    invoke-virtual {v0}, Lcom/google/android/gms/auth/api/signin/GoogleSignInClient;->revokeAccess()Lcom/google/android/gms/tasks/Task;

    sget-object v0, Lcom/facebook/login/LoginManager;->setDefaultImpl:Lcom/facebook/login/LoginManager$TaskDescription;

    invoke-virtual {v0}, Lcom/facebook/login/LoginManager$TaskDescription;->setDefaultImpl()Lcom/facebook/login/LoginManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/login/LoginManager;->onConnectionSuspended()V

    return-void
.end method

.method public final setDefaultImpl(I)V
    .locals 2

    sget-object v0, Lo/PrintWriter;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/tencent/mmkv/MMKV;

    const-string v1, "from_view_key"

    invoke-virtual {v0, v1, p1}, Lcom/tencent/mmkv/MMKV;->putInt(Ljava/lang/String;I)Landroid/content/SharedPreferences$Editor;

    return-void
.end method

.method public final setDefaultImpl(Lo/PrintWriter$Application;)V
    .locals 1
    .param p1    # Lo/PrintWriter$Application;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string v0, "listener"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lo/PrintWriter;->onConnectionFailed:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public final setDefaultImpl()Z
    .locals 1

    # Always return true to bypass VIP validation
    const/4 v0, 0x1

    return v0
.end method

.method public final write()Z
    .locals 1

    # Always return true to bypass VIP validation
    const/4 v0, 0x1

    return v0
.end method
