# Manual Testing Checklist - Unlock Screen Functionality

## Pre-Test Setup

### Requirements
- [ ] Android device (physical or emulator)
- [ ] ABox Voice Changer APK installed
- [ ] Fresh installation or cleared app data

### Setup Steps
1. [ ] Install the APK: `adb install ABox_Voice_Changer.apk`
2. [ ] Clear app data: `adb shell pm clear com.abox.apps`
3. [ ] Ensure device screen is unlocked and visible

---

## Test Case 1: First Launch - Unlock Screen Appears

### Steps
1. [ ] Launch the app from app drawer or via ADB:
   ```bash
   adb shell am start -n com.abox.apps/.activitys.SplashActivity
   ```
2. [ ] Observe the app launch sequence

### Expected Results
- [ ] Splash screen appears briefly
- [ ] Unlock screen appears with:
  - [ ] Dark background (#FF1A1A1A)
  - [ ] Title: "App Unlock Required"
  - [ ] Instruction: "Enter unlock code to continue:"
  - [ ] Empty text input field with hint "Unlock Code"
  - [ ] "UNLOCK" button

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Test Case 2: Wrong Unlock Code

### Steps
1. [ ] In the unlock code field, enter: `wrong123`
2. [ ] Tap the "UNLOCK" button
3. [ ] Observe the result

### Expected Results
- [ ] Toast message appears: "Invalid unlock code. Access denied."
- [ ] Remains on unlock screen (doesn't navigate away)
- [ ] Text field retains the entered text
- [ ] No app crash or freeze

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Test Case 3: Correct Unlock Code

### Steps
1. [ ] Clear the text field (select all and delete)
2. [ ] Enter the correct unlock code: `LOCK-5960`
3. [ ] Tap the "UNLOCK" button
4. [ ] Observe the result

### Expected Results
- [ ] No error toast appears
- [ ] App navigates away from unlock screen
- [ ] Next screen appears (AcquirePermissionGuideActivity)
- [ ] Unlock screen disappears completely

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Test Case 4: Unlock Status Persistence

### Steps
1. [ ] After successful unlock (Test Case 3), force close the app:
   ```bash
   adb shell am force-stop com.abox.apps
   ```
2. [ ] Wait 2-3 seconds
3. [ ] Launch the app again:
   ```bash
   adb shell am start -n com.abox.apps/.activitys.SplashActivity
   ```
4. [ ] Observe the launch sequence

### Expected Results
- [ ] Splash screen appears briefly
- [ ] Unlock screen is **skipped**
- [ ] App goes directly to the main app flow
- [ ] No unlock prompt appears

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Test Case 5: Data Reset Test

### Steps
1. [ ] Clear app data to reset unlock status:
   ```bash
   adb shell pm clear com.abox.apps
   ```
2. [ ] Launch the app again
3. [ ] Observe the behavior

### Expected Results
- [ ] Unlock screen appears again (like first launch)
- [ ] Previous unlock status is forgotten
- [ ] Behaves like Test Case 1

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Test Case 6: UI/UX Validation

### Steps
1. [ ] Launch app to unlock screen
2. [ ] Test various UI interactions

### UI Elements to Check
- [ ] Text is readable (white text on dark background)
- [ ] Text field accepts input properly
- [ ] Button is tappable and responsive
- [ ] Layout looks proper on device screen
- [ ] No UI elements are cut off or overlapping
- [ ] Keyboard appears when tapping text field
- [ ] Keyboard "Done" button works

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Test Case 7: Edge Cases

### Test 7a: Empty Input
1. [ ] Leave text field empty
2. [ ] Tap "UNLOCK" button
3. [ ] Expected: Error toast or no action

### Test 7b: Special Characters
1. [ ] Enter: `!@#$%^&*()`
2. [ ] Tap "UNLOCK" button
3. [ ] Expected: Error toast

### Test 7c: Case Sensitivity
1. [ ] Enter: `lock-5960` (lowercase)
2. [ ] Tap "UNLOCK" button
3. [ ] Expected: Error toast (case sensitive)

### Test 7d: Extra Spaces
1. [ ] Enter: ` LOCK-5960 ` (with spaces)
2. [ ] Tap "UNLOCK" button
3. [ ] Expected: Should work (code trims spaces)

### Pass/Fail: ⬜ PASS ⬜ FAIL
**Notes:** ________________________________

---

## Overall Test Results

### Summary
- **Total Test Cases:** 7
- **Passed:** ___/7
- **Failed:** ___/7

### Critical Issues Found
- [ ] None
- [ ] Minor issues (list below)
- [ ] Major issues (list below)

**Issue Details:**
_________________________________
_________________________________
_________________________________

### Final Assessment
- [ ] ✅ **PASS** - Unlock functionality works as expected
- [ ] ⚠️ **PASS WITH ISSUES** - Works but has minor problems
- [ ] ❌ **FAIL** - Critical functionality broken

### Recommendations
_________________________________
_________________________________
_________________________________

---

## Technical Verification (Optional)

### SharedPreferences Check
```bash
# Check if unlock status is saved
adb shell run-as com.abox.apps cat shared_prefs/unlock_prefs.xml
```

Expected content after unlock:
```xml
<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <boolean name="activated" value="true" />
</map>
```

### Log Monitoring
```bash
# Monitor app logs during testing
adb logcat | grep -i "abox\|unlock"
```

---

**Tester:** ________________________  
**Date:** __________________________  
**Device:** ________________________  
**Android Version:** _______________
