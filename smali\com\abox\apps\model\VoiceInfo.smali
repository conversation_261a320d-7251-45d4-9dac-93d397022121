.class public final Lcom/abox/apps/model/VoiceInfo;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008;\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u0087\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0001\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0008\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\t\u001a\u00020\n\u0012\u0008\u0008\u0002\u0010\u000b\u001a\u00020\n\u0012\u0008\u0008\u0002\u0010\u000c\u001a\u00020\n\u0012\n\u0008\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\u0008\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n\u0012\n\u0008\u0002\u0010\u0010\u001a\u0004\u0018\u00010\n\u0012\u0008\u0008\u0002\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0012J\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\u0010\u00108\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010%J\u0010\u00109\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010%J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010;\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010<\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\t\u0010=\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010>\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010?\u001a\u00020\nH\u00c6\u0003J\t\u0010@\u001a\u00020\nH\u00c6\u0003J\t\u0010A\u001a\u00020\nH\u00c6\u0003J\u000b\u0010B\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u0092\u0001\u0010C\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u00032\n\u0008\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00032\n\u0008\u0002\u0010\u0008\u001a\u0004\u0018\u00010\u00052\u0008\u0008\u0002\u0010\t\u001a\u00020\n2\u0008\u0008\u0002\u0010\u000b\u001a\u00020\n2\u0008\u0008\u0002\u0010\u000c\u001a\u00020\n2\n\u0008\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\u0008\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n2\n\u0008\u0002\u0010\u0010\u001a\u0004\u0018\u00010\n2\u0008\u0008\u0002\u0010\u0011\u001a\u00020\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010DJ\u0013\u0010E\u001a\u00020\n2\u0008\u0010F\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010G\u001a\u00020\u0003H\u00d6\u0001J\t\u0010H\u001a\u00020\u0005H\u00d6\u0001R\u001a\u0010\u000c\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0013\u0010\u0014\"\u0004\u0008\u0015\u0010\u0016R\u001c\u0010\u0006\u001a\u0004\u0018\u00010\u0001X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0017\u0010\u0018\"\u0004\u0008\u0019\u0010\u001aR\u001a\u0010\u0007\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u001b\u0010\u001c\"\u0004\u0008\u001d\u0010\u001eR\u001c\u0010\u0008\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u001f\u0010 \"\u0004\u0008!\u0010\"R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008#\u0010\u001c\"\u0004\u0008$\u0010\u001eR\u001e\u0010\u0010\u001a\u0004\u0018\u00010\nX\u0086\u000e\u00a2\u0006\u0010\n\u0002\u0010(\u001a\u0004\u0008\u0010\u0010%\"\u0004\u0008&\u0010\'R\u001c\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008)\u0010 \"\u0004\u0008*\u0010\"R\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008+\u0010\u0014\"\u0004\u0008,\u0010\u0016R\u001a\u0010\u000b\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008-\u0010\u0014\"\u0004\u0008.\u0010\u0016R\u001e\u0010\u000f\u001a\u0004\u0018\u00010\nX\u0086\u000e\u00a2\u0006\u0010\n\u0002\u0010(\u001a\u0004\u0008/\u0010%\"\u0004\u00080\u0010\'R\u001c\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00081\u00102\"\u0004\u00083\u00104R\u001a\u0010\u0011\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00085\u0010\u001c\"\u0004\u00086\u0010\u001e\u00a8\u0006I"
    }
    d2 = {
        "Lcom/abox/apps/model/VoiceInfo;",
        "",
        "id",
        "",
        "name",
        "",
        "cover",
        "coverResId",
        "coverUrl",
        "selectedStatus",
        "",
        "showInMainView",
        "checkedStatus",
        "voiceConfig",
        "Lcom/abox/audiotransform/VoiceConfig;",
        "vip",
        "isFree",
        "voiceType",
        "(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)V",
        "getCheckedStatus",
        "()Z",
        "setCheckedStatus",
        "(Z)V",
        "getCover",
        "()Ljava/lang/Object;",
        "setCover",
        "(Ljava/lang/Object;)V",
        "getCoverResId",
        "()I",
        "setCoverResId",
        "(I)V",
        "getCoverUrl",
        "()Ljava/lang/String;",
        "setCoverUrl",
        "(Ljava/lang/String;)V",
        "getId",
        "setId",
        "()Ljava/lang/Boolean;",
        "setFree",
        "(Ljava/lang/Boolean;)V",
        "Ljava/lang/Boolean;",
        "getName",
        "setName",
        "getSelectedStatus",
        "setSelectedStatus",
        "getShowInMainView",
        "setShowInMainView",
        "getVip",
        "setVip",
        "getVoiceConfig",
        "()Lcom/abox/audiotransform/VoiceConfig;",
        "setVoiceConfig",
        "(Lcom/abox/audiotransform/VoiceConfig;)V",
        "getVoiceType",
        "setVoiceType",
        "component1",
        "component10",
        "component11",
        "component12",
        "component2",
        "component3",
        "component4",
        "component5",
        "component6",
        "component7",
        "component8",
        "component9",
        "copy",
        "(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)Lcom/abox/apps/model/VoiceInfo;",
        "equals",
        "other",
        "hashCode",
        "toString",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private checkedStatus:Z

.field private cover:Ljava/lang/Object;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private coverResId:I

.field private coverUrl:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private id:I

.field private isFree:Ljava/lang/Boolean;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private name:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private selectedStatus:Z

.field private showInMainView:Z

.field private vip:Ljava/lang/Boolean;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private voiceConfig:Lcom/abox/audiotransform/VoiceConfig;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private voiceType:I


# direct methods
.method public constructor <init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)V
    .locals 0
    .param p2    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p9    # Lcom/abox/audiotransform/VoiceConfig;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p10    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p11    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    iput-object p2, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    iput-object p3, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    iput p4, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    iput-object p5, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    iput-boolean p6, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    iput-boolean p7, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    iput-boolean p8, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    iput-object p9, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    iput-object p10, p0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    iput-object p11, p0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    iput p12, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    return-void
.end method

.method public synthetic constructor <init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 11

    move/from16 v0, p13

    and-int/lit8 v1, v0, 0x2

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    move-object v1, v2

    goto :goto_0

    :cond_0
    move-object v1, p2

    :goto_0
    and-int/lit8 v3, v0, 0x4

    if-eqz v3, :cond_1

    move-object v3, v2

    goto :goto_1

    :cond_1
    move-object v3, p3

    :goto_1
    and-int/lit8 v4, v0, 0x8

    const/4 v5, 0x0

    if-eqz v4, :cond_2

    move v4, v5

    goto :goto_2

    :cond_2
    move v4, p4

    :goto_2
    and-int/lit8 v6, v0, 0x10

    if-eqz v6, :cond_3

    move-object v6, v2

    goto :goto_3

    :cond_3
    move-object/from16 v6, p5

    :goto_3
    and-int/lit8 v7, v0, 0x20

    if-eqz v7, :cond_4

    move v7, v5

    goto :goto_4

    :cond_4
    move/from16 v7, p6

    :goto_4
    and-int/lit8 v8, v0, 0x40

    if-eqz v8, :cond_5

    const/4 v8, 0x1

    goto :goto_5

    :cond_5
    move/from16 v8, p7

    :goto_5
    and-int/lit16 v9, v0, 0x80

    if-eqz v9, :cond_6

    goto :goto_6

    :cond_6
    move/from16 v5, p8

    :goto_6
    and-int/lit16 v9, v0, 0x100

    if-eqz v9, :cond_7

    goto :goto_7

    :cond_7
    move-object/from16 v2, p9

    :goto_7
    and-int/lit16 v9, v0, 0x200

    if-eqz v9, :cond_8

    sget-object v9, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    goto :goto_8

    :cond_8
    move-object/from16 v9, p10

    :goto_8
    and-int/lit16 v10, v0, 0x400

    if-eqz v10, :cond_9

    sget-object v10, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    goto :goto_9

    :cond_9
    move-object/from16 v10, p11

    :goto_9
    and-int/lit16 v0, v0, 0x800

    if-eqz v0, :cond_a

    const/4 v0, 0x2

    goto :goto_a

    :cond_a
    move/from16 v0, p12

    :goto_a
    move-object p2, p0

    move p3, p1

    move-object p4, v1

    move-object/from16 p5, v3

    move/from16 p6, v4

    move-object/from16 p7, v6

    move/from16 p8, v7

    move/from16 p9, v8

    move/from16 p10, v5

    move-object/from16 p11, v2

    move-object/from16 p12, v9

    move-object/from16 p13, v10

    move/from16 p14, v0

    invoke-direct/range {p2 .. p14}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)V

    return-void
.end method

.method public static synthetic copy$default(Lcom/abox/apps/model/VoiceInfo;ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILjava/lang/Object;)Lcom/abox/apps/model/VoiceInfo;
    .locals 13

    move-object v0, p0

    move/from16 v1, p13

    and-int/lit8 v2, v1, 0x1

    if-eqz v2, :cond_0

    iget v2, v0, Lcom/abox/apps/model/VoiceInfo;->id:I

    goto :goto_0

    :cond_0
    move v2, p1

    :goto_0
    and-int/lit8 v3, v1, 0x2

    if-eqz v3, :cond_1

    iget-object v3, v0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    goto :goto_1

    :cond_1
    move-object v3, p2

    :goto_1
    and-int/lit8 v4, v1, 0x4

    if-eqz v4, :cond_2

    iget-object v4, v0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    goto :goto_2

    :cond_2
    move-object/from16 v4, p3

    :goto_2
    and-int/lit8 v5, v1, 0x8

    if-eqz v5, :cond_3

    iget v5, v0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    goto :goto_3

    :cond_3
    move/from16 v5, p4

    :goto_3
    and-int/lit8 v6, v1, 0x10

    if-eqz v6, :cond_4

    iget-object v6, v0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    goto :goto_4

    :cond_4
    move-object/from16 v6, p5

    :goto_4
    and-int/lit8 v7, v1, 0x20

    if-eqz v7, :cond_5

    iget-boolean v7, v0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    goto :goto_5

    :cond_5
    move/from16 v7, p6

    :goto_5
    and-int/lit8 v8, v1, 0x40

    if-eqz v8, :cond_6

    iget-boolean v8, v0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    goto :goto_6

    :cond_6
    move/from16 v8, p7

    :goto_6
    and-int/lit16 v9, v1, 0x80

    if-eqz v9, :cond_7

    iget-boolean v9, v0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    goto :goto_7

    :cond_7
    move/from16 v9, p8

    :goto_7
    and-int/lit16 v10, v1, 0x100

    if-eqz v10, :cond_8

    iget-object v10, v0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    goto :goto_8

    :cond_8
    move-object/from16 v10, p9

    :goto_8
    and-int/lit16 v11, v1, 0x200

    if-eqz v11, :cond_9

    iget-object v11, v0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    goto :goto_9

    :cond_9
    move-object/from16 v11, p10

    :goto_9
    and-int/lit16 v12, v1, 0x400

    if-eqz v12, :cond_a

    iget-object v12, v0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    goto :goto_a

    :cond_a
    move-object/from16 v12, p11

    :goto_a
    and-int/lit16 v1, v1, 0x800

    if-eqz v1, :cond_b

    iget v1, v0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    goto :goto_b

    :cond_b
    move/from16 v1, p12

    :goto_b
    move p1, v2

    move-object p2, v3

    move-object/from16 p3, v4

    move/from16 p4, v5

    move-object/from16 p5, v6

    move/from16 p6, v7

    move/from16 p7, v8

    move/from16 p8, v9

    move-object/from16 p9, v10

    move-object/from16 p10, v11

    move-object/from16 p11, v12

    move/from16 p12, v1

    invoke-virtual/range {p0 .. p12}, Lcom/abox/apps/model/VoiceInfo;->copy(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)Lcom/abox/apps/model/VoiceInfo;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public final component1()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    return v0
.end method

.method public final component10()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final component11()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final component12()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    return v0
.end method

.method public final component2()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    return-object v0
.end method

.method public final component3()Ljava/lang/Object;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    return-object v0
.end method

.method public final component4()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    return v0
.end method

.method public final component5()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final component6()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    return v0
.end method

.method public final component7()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    return v0
.end method

.method public final component8()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    return v0
.end method

.method public final component9()Lcom/abox/audiotransform/VoiceConfig;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    return-object v0
.end method

.method public final copy(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)Lcom/abox/apps/model/VoiceInfo;
    .locals 14
    .param p2    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p9    # Lcom/abox/audiotransform/VoiceConfig;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p10    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p11    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    new-instance v13, Lcom/abox/apps/model/VoiceInfo;

    move-object v0, v13

    move v1, p1

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    move/from16 v4, p4

    move-object/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    move-object/from16 v9, p9

    move-object/from16 v10, p10

    move-object/from16 v11, p11

    move/from16 v12, p12

    invoke-direct/range {v0 .. v12}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;I)V

    return-object v13
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lcom/abox/apps/model/VoiceInfo;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lcom/abox/apps/model/VoiceInfo;

    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    iget v3, p1, Lcom/abox/apps/model/VoiceInfo;->id:I

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    iget-object v3, p1, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    iget v3, p1, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    if-eq v1, v3, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    return v2

    :cond_6
    iget-boolean v1, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    iget-boolean v3, p1, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    if-eq v1, v3, :cond_7

    return v2

    :cond_7
    iget-boolean v1, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    iget-boolean v3, p1, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    if-eq v1, v3, :cond_8

    return v2

    :cond_8
    iget-boolean v1, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    iget-boolean v3, p1, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    if-eq v1, v3, :cond_9

    return v2

    :cond_9
    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    iget-object v3, p1, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_a

    return v2

    :cond_a
    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    iget-object v3, p1, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_b

    return v2

    :cond_b
    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    iget-object v3, p1, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_c

    return v2

    :cond_c
    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    iget p1, p1, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    if-eq v1, p1, :cond_d

    return v2

    :cond_d
    return v0
.end method

.method public final getCheckedStatus()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    return v0
.end method

.method public final getCover()Ljava/lang/Object;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    return-object v0
.end method

.method public final getCoverResId()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    return v0
.end method

.method public final getCoverUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final getId()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    return v0
.end method

.method public final getName()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    return-object v0
.end method

.method public final getSelectedStatus()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    return v0
.end method

.method public final getShowInMainView()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    return v0
.end method

.method public final getVip()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    # Always return false to hide VIP icons in UI
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final getVoiceConfig()Lcom/abox/audiotransform/VoiceConfig;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    return-object v0
.end method

.method public final getVoiceType()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    return v0
.end method

.method public hashCode()I
    .locals 12

    iget v0, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    invoke-static {v0}, Ljava/lang/Integer;->hashCode(I)I

    move-result v0

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    const/4 v2, 0x0

    if-nez v1, :cond_0

    move v1, v2

    goto :goto_0

    :cond_0
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_0
    iget-object v3, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    if-nez v3, :cond_1

    move v3, v2

    goto :goto_1

    :cond_1
    invoke-virtual {v3}, Ljava/lang/Object;->hashCode()I

    move-result v3

    :goto_1
    iget v4, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    invoke-static {v4}, Ljava/lang/Integer;->hashCode(I)I

    move-result v4

    iget-object v5, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    if-nez v5, :cond_2

    move v5, v2

    goto :goto_2

    :cond_2
    invoke-virtual {v5}, Ljava/lang/String;->hashCode()I

    move-result v5

    :goto_2
    iget-boolean v6, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    const/4 v7, 0x1

    if-eqz v6, :cond_3

    move v6, v7

    :cond_3
    iget-boolean v8, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    if-eqz v8, :cond_4

    move v8, v7

    :cond_4
    iget-boolean v9, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    if-eqz v9, :cond_5

    goto :goto_3

    :cond_5
    move v7, v9

    :goto_3
    iget-object v9, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    if-nez v9, :cond_6

    move v9, v2

    goto :goto_4

    :cond_6
    invoke-virtual {v9}, Lcom/abox/audiotransform/VoiceConfig;->hashCode()I

    move-result v9

    :goto_4
    iget-object v10, p0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    if-nez v10, :cond_7

    move v10, v2

    goto :goto_5

    :cond_7
    invoke-virtual {v10}, Ljava/lang/Object;->hashCode()I

    move-result v10

    :goto_5
    iget-object v11, p0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    if-nez v11, :cond_8

    goto :goto_6

    :cond_8
    invoke-virtual {v11}, Ljava/lang/Object;->hashCode()I

    move-result v2

    :goto_6
    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v3

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v4

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v5

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v6

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v8

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v7

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v9

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v10

    mul-int/lit8 v0, v0, 0x1f

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    invoke-static {v1}, Ljava/lang/Integer;->hashCode(I)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public final isFree()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    # Always return true to show all voices as free
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final setCheckedStatus(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    return-void
.end method

.method public final setCover(Ljava/lang/Object;)V
    .locals 0
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    return-void
.end method

.method public final setCoverResId(I)V
    .locals 0

    iput p1, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    return-void
.end method

.method public final setCoverUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    return-void
.end method

.method public final setFree(Ljava/lang/Boolean;)V
    .locals 0
    .param p1    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    return-void
.end method

.method public final setId(I)V
    .locals 0

    iput p1, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    return-void
.end method

.method public final setName(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    return-void
.end method

.method public final setSelectedStatus(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    return-void
.end method

.method public final setShowInMainView(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    return-void
.end method

.method public final setVip(Ljava/lang/Boolean;)V
    .locals 0
    .param p1    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    return-void
.end method

.method public final setVoiceConfig(Lcom/abox/audiotransform/VoiceConfig;)V
    .locals 0
    .param p1    # Lcom/abox/audiotransform/VoiceConfig;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    return-void
.end method

.method public final setVoiceType(I)V
    .locals 0

    iput p1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 2
    .annotation build Lo/cbz;
    .end annotation

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "VoiceInfo(id="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->id:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", name="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", cover="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->cover:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", coverResId="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->coverResId:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", coverUrl="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->coverUrl:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", selectedStatus="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Lcom/abox/apps/model/VoiceInfo;->selectedStatus:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, ", showInMainView="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Lcom/abox/apps/model/VoiceInfo;->showInMainView:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, ", checkedStatus="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Lcom/abox/apps/model/VoiceInfo;->checkedStatus:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, ", voiceConfig="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceConfig:Lcom/abox/audiotransform/VoiceConfig;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", vip="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->vip:Ljava/lang/Boolean;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", isFree="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/VoiceInfo;->isFree:Ljava/lang/Boolean;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", voiceType="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/abox/apps/model/VoiceInfo;->voiceType:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
