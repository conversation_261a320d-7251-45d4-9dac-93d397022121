.class public final Lo/ObjectOutput;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008%\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0000\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010.\u001a\u00020/2\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u00020\t2\u0008\u0008\u0002\u00103\u001a\u00020/J\u0018\u00104\u001a\u0002052\u0006\u00102\u001a\u00020\t2\u0008\u0008\u0002\u00106\u001a\u00020/J\u0016\u00107\u001a\u0012\u0012\u0004\u0012\u00020\t08j\u0008\u0012\u0004\u0012\u00020\t`9J4\u0010:\u001a\u0012\u0012\u0004\u0012\u00020\t08j\u0008\u0012\u0004\u0012\u00020\t`92\u0008\u0008\u0002\u0010;\u001a\u00020/2\u0008\u0008\u0002\u0010<\u001a\u00020/2\u0008\u0008\u0002\u0010=\u001a\u00020/J\u000e\u0010>\u001a\u0002052\u0006\u0010?\u001a\u00020@R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0008\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\n\u0010\u000bR\u0011\u0010\u000c\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\r\u0010\u000bR\u0011\u0010\u000e\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u000f\u0010\u000bR\u0011\u0010\u0010\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0011\u0010\u000bR\u0011\u0010\u0012\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0013\u0010\u000bR\u0011\u0010\u0014\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0015\u0010\u000bR\u0011\u0010\u0016\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0017\u0010\u000bR\u0011\u0010\u0018\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0019\u0010\u000bR\u0011\u0010\u001a\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u001b\u0010\u000bR\u0011\u0010\u001c\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u001d\u0010\u000bR\u0011\u0010\u001e\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u001f\u0010\u000bR\u0011\u0010 \u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008!\u0010\u000bR\u0011\u0010\"\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008#\u0010\u000bR\u0011\u0010$\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008%\u0010\u000bR\u0011\u0010&\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\'\u0010\u000bR\u0011\u0010(\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008)\u0010\u000bR\u0011\u0010*\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008+\u0010\u000bR\u0011\u0010,\u001a\u00020\t\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008-\u0010\u000b\u00a8\u0006A"
    }
    d2 = {
        "Lcom/abox/apps/manager/VoiceManager;",
        "",
        "()V",
        "COMING_SOON_ID",
        "",
        "DEFAULT_SELECTED_VOICE_ID",
        "DEFAULT_SELECT_ID_KEY",
        "",
        "Shiloh",
        "Lcom/abox/apps/model/VoiceInfo;",
        "getShiloh",
        "()Lcom/abox/apps/model/VoiceInfo;",
        "aiBoy",
        "getAiBoy",
        "aiBoy2",
        "getAiBoy2",
        "aiFemale",
        "getAiFemale",
        "aiFemale2",
        "getAiFemale2",
        "aiFemale3",
        "getAiFemale3",
        "aiFemale4",
        "getAiFemale4",
        "aiFemale5",
        "getAiFemale5",
        "aiGirl",
        "getAiGirl",
        "aiGirl2",
        "getAiGirl2",
        "aiGirl3",
        "getAiGirl3",
        "aiMale",
        "getAiMale",
        "aiMale2",
        "getAiMale2",
        "aiMale3",
        "getAiMale3",
        "aiMale4",
        "getAiMale4",
        "herman",
        "getHerman",
        "monster",
        "getMonster",
        "originalVoice",
        "getOriginalVoice",
        "canUseVoice",
        "",
        "context",
        "Landroid/content/Context;",
        "voiceInfo",
        "fromFloatBall",
        "changeVoiceConfig",
        "",
        "fromHome",
        "getExperienceVoiceList",
        "Ljava/util/ArrayList;",
        "Lkotlin/collections/ArrayList;",
        "getVoiceList",
        "showComing",
        "fromMain",
        "forExpand",
        "notifyThirdAppVoiceConfig",
        "voiceConfig",
        "Lcom/abox/audiotransform/VoiceConfig;",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nVoiceManager.kt\nKotlin\n*S Kotlin\n*F\n+ 1 VoiceManager.kt\ncom/abox/apps/manager/VoiceManager\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,375:1\n1855#2,2:376\n1855#2,2:378\n766#2:380\n857#2,2:381\n*S KotlinDebug\n*F\n+ 1 VoiceManager.kt\ncom/abox/apps/manager/VoiceManager\n*L\n293#1:376,2\n298#1:378,2\n303#1:380\n303#1:381,2\n*E\n"
    }
.end annotation


# static fields
.field private static final IconCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaBrowserCompat$CustomActionResultReceiver:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaBrowserCompat$ItemReceiver:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaBrowserCompat$MediaItem:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaBrowserCompat$SearchResultReceiver:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaDescriptionCompat:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaMetadataCompat:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final MediaSessionCompat$Token:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final RatingCompat:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final RemoteActionCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final asBinder:I = 0x1e

.field private static final asInterface:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final getDefaultImpl:I = -0x1

.field private static final onConnected:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final onConnectionFailed:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final onConnectionSuspended:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final onTransact:Lo/ObjectOutput;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final read:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final setDefaultImpl:Ljava/lang/String; = "default_select_id"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final setInternalConnectionCallback:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final write:Lcom/abox/apps/model/VoiceInfo;
    .annotation build Lo/cbz;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 30

    new-instance v0, Lo/ObjectOutput;

    invoke-direct {v0}, Lo/ObjectOutput;-><init>()V

    sput-object v0, Lo/ObjectOutput;->onTransact:Lo/ObjectOutput;

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v0

    sget v1, Lo/Cursor$TaskStackBuilder;->$r8$lambda$K-rBLxNpMJdSxVU3Lsj65hn0UyA:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v4

    sget v0, Lo/Cursor$FragmentManager;->onMenuItemSelected:I

    new-instance v1, Lcom/abox/audiotransform/VoiceConfig;

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/high16 v9, 0x3f800000    # 1.0f

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/16 v12, 0x37

    const/4 v13, 0x0

    move-object v5, v1

    invoke-direct/range {v5 .. v13}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v17, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/4 v3, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v12, 0x0

    const/4 v14, 0x0

    const/16 v15, 0xef8

    const/16 v16, 0x0

    move-object/from16 v2, v17

    move-object v11, v1

    invoke-direct/range {v2 .. v16}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v17, Lo/ObjectOutput;->MediaSessionCompat$Token:Lcom/abox/apps/model/VoiceInfo;

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v0

    sget v1, Lo/Cursor$TaskStackBuilder;->onViewAttachedToWindow:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v4

    sget v0, Lo/Cursor$FragmentManager;->onBackPressed:I

    new-instance v1, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v6, 0x21

    const/4 v7, 0x1

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const-string v11, "id_33.wav"

    const/16 v12, 0x1c

    move-object v5, v1

    invoke-direct/range {v5 .. v13}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sget-object v29, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    new-instance v17, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/16 v3, 0x21

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v14, 0x1

    const/16 v15, 0x4f8

    move-object/from16 v2, v17

    move-object v11, v1

    move-object/from16 v12, v29

    invoke-direct/range {v2 .. v16}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v17, Lo/ObjectOutput;->write:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->MediaBrowserCompat$CustomActionResultReceiver:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->onConfigurationChanged:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x23

    const/4 v3, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const-string v7, "id_35.wav"

    const/16 v8, 0x1c

    const/4 v9, 0x0

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x23

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x0

    const/16 v21, 0x0

    const/16 v22, 0x0

    const/16 v25, 0x0

    const/16 v26, 0x1

    const/16 v27, 0x4f8

    const/16 v28, 0x0

    move-object v14, v1

    move-object/from16 v24, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->onConnected:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->MediaBrowserCompat$ItemReceiver:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->OnBackPressedDispatcher$OnBackPressedCancellable:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x25

    const-string v7, "id_37.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x25

    move-object v14, v1

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->RemoteActionCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->onConnectionSuspended:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->getDefaultImpl:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x1e

    const-string v7, "id_30.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x1e

    const/16 v24, 0x0

    const/16 v27, 0x2f8

    move-object v14, v1

    move-object/from16 v25, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->onConnectionFailed:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->setInternalConnectionCallback:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->ComponentActivity$$ExternalSyntheticLambda1:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x26

    const-string v7, "id_38.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x26

    const/16 v25, 0x0

    const/16 v27, 0x4f8

    move-object v14, v1

    move-object/from16 v24, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->MediaBrowserCompat$ItemReceiver:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->setEnabledChangedCallback$activity_release:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->fullyDrawnReported:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x20

    const-string v7, "id_32.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x20

    move-object v14, v1

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->onConnectionFailed:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->ComponentActivity$$ExternalSyntheticLambda3:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/4 v2, 0x6

    const-string v7, "id_6.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/4 v15, 0x6

    const/16 v24, 0x0

    const/16 v27, 0x2f8

    move-object v14, v1

    move-object/from16 v25, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->onConnectionSuspended:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->MediaBrowserCompat$MediaItem:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->asBinder:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0xa

    const-string v7, "id_10.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0xa

    const/16 v25, 0x0

    const/16 v27, 0x4f8

    move-object v14, v1

    move-object/from16 v24, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->setInternalConnectionCallback:Lcom/abox/apps/model/VoiceInfo;

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v0

    sget v1, Lo/Cursor$TaskStackBuilder;->ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->onMultiWindowModeChanged:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x27

    const-string v7, "id_39.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x27

    move-object v14, v1

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->read:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->IconCompatParcelizer:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->setDefaultImpl:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x8

    const-string v7, "id_8.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x8

    move-object v14, v1

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->IconCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->clearAvailableContext:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->OnBackPressedDispatcherKt:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0xf

    const-string v7, "id_15.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0xf

    move-object v14, v1

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->MediaMetadataCompat:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->RatingCompat:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->OnBackPressedDispatcherKt$addCallback$callback$1:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/4 v2, 0x3

    const-string v7, "id_3.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/4 v15, 0x3

    const/16 v24, 0x0

    const/16 v27, 0x2f8

    move-object v14, v1

    move-object/from16 v25, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->MediaDescriptionCompat:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->MediaBrowserCompat$SearchResultReceiver:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->ComponentActivity$1:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x29

    const/high16 v5, 0x40a00000    # 5.0f

    const-string v7, "id_41.wav"

    const/16 v8, 0x14

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x29

    const/16 v25, 0x0

    const/16 v27, 0x4f8

    move-object v14, v1

    move-object/from16 v24, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->MediaBrowserCompat$MediaItem:Lcom/abox/apps/model/VoiceInfo;

    sget v0, Lo/Cursor$TaskStackBuilder;->MediaDescriptionCompat:I

    invoke-static {v0}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v16

    sget v0, Lo/Cursor$FragmentManager;->asInterface:I

    new-instance v23, Lcom/abox/audiotransform/VoiceConfig;

    const/16 v2, 0x24

    const-string v7, "id_36.wav"

    move-object/from16 v1, v23

    invoke-direct/range {v1 .. v9}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v1, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    const/16 v15, 0x24

    const/16 v24, 0x0

    const/16 v27, 0x2f8

    move-object v14, v1

    move-object/from16 v25, v29

    invoke-direct/range {v14 .. v28}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v1, Lo/ObjectOutput;->MediaBrowserCompat$SearchResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v0

    sget v1, Lo/Cursor$TaskStackBuilder;->ComponentActivity$Api19Impl:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v4

    sget v0, Lo/Cursor$FragmentManager;->onNewIntent:I

    new-instance v1, Lcom/abox/audiotransform/VoiceConfig;

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/high16 v9, -0x3f400000    # -6.0f

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/16 v12, 0x37

    move-object v5, v1

    invoke-direct/range {v5 .. v13}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v17, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/16 v3, 0x35

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v12, 0x0

    const/4 v14, 0x0

    const/16 v15, 0xef8

    const/16 v16, 0x0

    move-object/from16 v2, v17

    move-object v11, v1

    invoke-direct/range {v2 .. v16}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v17, Lo/ObjectOutput;->MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v0

    sget v1, Lo/Cursor$TaskStackBuilder;->createOnBackInvokedCallback$lambda$0:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v4

    sget v0, Lo/Cursor$FragmentManager;->onActivityResult:I

    new-instance v1, Lcom/abox/audiotransform/VoiceConfig;

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/high16 v9, 0x40a00000    # 5.0f

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/16 v12, 0x37

    move-object v5, v1

    invoke-direct/range {v5 .. v13}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v17, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/16 v3, 0x33

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v12, 0x0

    move-object/from16 v2, v17

    move-object v11, v1

    invoke-direct/range {v2 .. v16}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v17, Lo/ObjectOutput;->asInterface:Lcom/abox/apps/model/VoiceInfo;

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object v0

    sget v1, Lo/Cursor$TaskStackBuilder;->onRequestPermissionsResult:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v4

    sget v0, Lo/Cursor$FragmentManager;->onCreatePanelMenu:I

    new-instance v1, Lcom/abox/audiotransform/VoiceConfig;

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/high16 v9, -0x3fc00000    # -3.0f

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/16 v12, 0x37

    move-object v5, v1

    invoke-direct/range {v5 .. v13}, Lcom/abox/audiotransform/VoiceConfig;-><init>(IIFFFLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v17, Lcom/abox/apps/model/VoiceInfo;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/16 v3, 0x34

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v12, 0x0

    move-object/from16 v2, v17

    move-object v11, v1

    invoke-direct/range {v2 .. v16}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v17, Lo/ObjectOutput;->RatingCompat:Lcom/abox/apps/model/VoiceInfo;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic asInterface(Lo/ObjectOutput;ZZZILjava/lang/Object;)Ljava/util/ArrayList;
    .locals 1

    and-int/lit8 p5, p4, 0x1

    const/4 v0, 0x0

    if-eqz p5, :cond_0

    move p1, v0

    :cond_0
    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_1

    const/4 p2, 0x1

    :cond_1
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_2

    move p3, v0

    :cond_2
    invoke-virtual {p0, p1, p2, p3}, Lo/ObjectOutput;->asInterface(ZZZ)Ljava/util/ArrayList;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic asInterface(Lo/ObjectOutput;Lcom/abox/apps/model/VoiceInfo;ZILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    :cond_0
    invoke-virtual {p0, p1, p2}, Lo/ObjectOutput;->setDefaultImpl(Lcom/abox/apps/model/VoiceInfo;Z)V

    return-void
.end method

.method public static synthetic getDefaultImpl(Lo/ObjectOutput;Landroid/content/Context;Lcom/abox/apps/model/VoiceInfo;ZILjava/lang/Object;)Z
    .locals 0

    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_0

    const/4 p3, 0x0

    :cond_0
    invoke-virtual {p0, p1, p2, p3}, Lo/ObjectOutput;->asBinder(Landroid/content/Context;Lcom/abox/apps/model/VoiceInfo;Z)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final IconCompatParcelizer()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->setInternalConnectionCallback:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final MediaBrowserCompat$CustomActionResultReceiver()Ljava/util/ArrayList;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/ArrayList<",
            "Lcom/abox/apps/model/VoiceInfo;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation

    const/4 v0, 0x4

    new-array v0, v0, [Lcom/abox/apps/model/VoiceInfo;

    sget-object v1, Lo/ObjectOutput;->write:Lcom/abox/apps/model/VoiceInfo;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lo/ObjectOutput;->onConnected:Lcom/abox/apps/model/VoiceInfo;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lo/ObjectOutput;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lo/ObjectOutput;->MediaMetadataCompat:Lcom/abox/apps/model/VoiceInfo;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->arrayListOf([Ljava/lang/Object;)Ljava/util/ArrayList;

    move-result-object v0

    return-object v0
.end method

.method public final MediaBrowserCompat$ItemReceiver()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaBrowserCompat$SearchResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final MediaBrowserCompat$MediaItem()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->RatingCompat:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final MediaBrowserCompat$SearchResultReceiver()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final MediaDescriptionCompat()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->asInterface:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final MediaMetadataCompat()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaSessionCompat$Token:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final RemoteActionCompatParcelizer()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->onConnectionSuspended:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final asBinder()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->IconCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final asBinder(Landroid/content/Context;Lcom/abox/apps/model/VoiceInfo;Z)Z
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .param p2    # Lcom/abox/apps/model/VoiceInfo;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    # Always return true to bypass VIP validation for voice selection
    const/4 v0, 0x1

    return v0
.end method

.method public final asInterface()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->write:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final asInterface(ZZZ)Ljava/util/ArrayList;
    .locals 32
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZZ)",
            "Ljava/util/ArrayList<",
            "Lcom/abox/apps/model/VoiceInfo;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation

    const/16 v1, 0xf

    const/16 v2, 0xe

    const/16 v3, 0xd

    const/16 v4, 0xc

    const/16 v5, 0xb

    const/16 v6, 0xa

    const/16 v7, 0x9

    const/16 v8, 0x8

    const/4 v9, 0x7

    const/4 v10, 0x6

    const/4 v11, 0x5

    const/4 v12, 0x4

    const/4 v13, 0x3

    const/4 v14, 0x2

    const/16 v15, 0x11

    const/16 v16, 0x1

    const/4 v0, 0x0

    if-eqz p3, :cond_0

    new-array v15, v15, [Lcom/abox/apps/model/VoiceInfo;

    sget-object v18, Lo/ObjectOutput;->onConnectionSuspended:Lcom/abox/apps/model/VoiceInfo;

    aput-object v18, v15, v0

    sget-object v18, Lo/ObjectOutput;->onConnected:Lcom/abox/apps/model/VoiceInfo;

    aput-object v18, v15, v16

    sget-object v18, Lo/ObjectOutput;->MediaBrowserCompat$SearchResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v18, v15, v14

    sget-object v14, Lo/ObjectOutput;->RemoteActionCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    aput-object v14, v15, v13

    sget-object v13, Lo/ObjectOutput;->onConnectionFailed:Lcom/abox/apps/model/VoiceInfo;

    aput-object v13, v15, v12

    sget-object v12, Lo/ObjectOutput;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v12, v15, v11

    sget-object v11, Lo/ObjectOutput;->MediaDescriptionCompat:Lcom/abox/apps/model/VoiceInfo;

    aput-object v11, v15, v10

    sget-object v10, Lo/ObjectOutput;->MediaMetadataCompat:Lcom/abox/apps/model/VoiceInfo;

    aput-object v10, v15, v9

    sget-object v9, Lo/ObjectOutput;->IconCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    aput-object v9, v15, v8

    sget-object v8, Lo/ObjectOutput;->read:Lcom/abox/apps/model/VoiceInfo;

    aput-object v8, v15, v7

    sget-object v7, Lo/ObjectOutput;->setInternalConnectionCallback:Lcom/abox/apps/model/VoiceInfo;

    aput-object v7, v15, v6

    sget-object v6, Lo/ObjectOutput;->asInterface:Lcom/abox/apps/model/VoiceInfo;

    aput-object v6, v15, v5

    sget-object v5, Lo/ObjectOutput;->MediaBrowserCompat$ItemReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v5, v15, v4

    sget-object v4, Lo/ObjectOutput;->RatingCompat:Lcom/abox/apps/model/VoiceInfo;

    aput-object v4, v15, v3

    sget-object v3, Lo/ObjectOutput;->MediaBrowserCompat$MediaItem:Lcom/abox/apps/model/VoiceInfo;

    aput-object v3, v15, v2

    sget-object v2, Lo/ObjectOutput;->MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v2, v15, v1

    sget-object v1, Lo/ObjectOutput;->write:Lcom/abox/apps/model/VoiceInfo;

    const/16 v2, 0x10

    aput-object v1, v15, v2

    invoke-static {v15}, Lkotlin/collections/CollectionsKt;->arrayListOf([Ljava/lang/Object;)Ljava/util/ArrayList;

    move-result-object v1

    goto :goto_0

    :cond_0
    new-array v15, v15, [Lcom/abox/apps/model/VoiceInfo;

    sget-object v18, Lo/ObjectOutput;->onConnectionSuspended:Lcom/abox/apps/model/VoiceInfo;

    aput-object v18, v15, v0

    sget-object v18, Lo/ObjectOutput;->MediaBrowserCompat$SearchResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v18, v15, v16

    sget-object v18, Lo/ObjectOutput;->onConnectionFailed:Lcom/abox/apps/model/VoiceInfo;

    aput-object v18, v15, v14

    sget-object v14, Lo/ObjectOutput;->MediaDescriptionCompat:Lcom/abox/apps/model/VoiceInfo;

    aput-object v14, v15, v13

    sget-object v13, Lo/ObjectOutput;->IconCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    aput-object v13, v15, v12

    sget-object v12, Lo/ObjectOutput;->setInternalConnectionCallback:Lcom/abox/apps/model/VoiceInfo;

    aput-object v12, v15, v11

    sget-object v11, Lo/ObjectOutput;->MediaBrowserCompat$ItemReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v11, v15, v10

    sget-object v10, Lo/ObjectOutput;->MediaBrowserCompat$MediaItem:Lcom/abox/apps/model/VoiceInfo;

    aput-object v10, v15, v9

    sget-object v9, Lo/ObjectOutput;->write:Lcom/abox/apps/model/VoiceInfo;

    aput-object v9, v15, v8

    sget-object v8, Lo/ObjectOutput;->onConnected:Lcom/abox/apps/model/VoiceInfo;

    aput-object v8, v15, v7

    sget-object v7, Lo/ObjectOutput;->RemoteActionCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    aput-object v7, v15, v6

    sget-object v6, Lo/ObjectOutput;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    aput-object v6, v15, v5

    sget-object v5, Lo/ObjectOutput;->MediaMetadataCompat:Lcom/abox/apps/model/VoiceInfo;

    aput-object v5, v15, v4

    sget-object v4, Lo/ObjectOutput;->read:Lcom/abox/apps/model/VoiceInfo;

    aput-object v4, v15, v3

    sget-object v3, Lo/ObjectOutput;->asInterface:Lcom/abox/apps/model/VoiceInfo;

    aput-object v3, v15, v2

    sget-object v2, Lo/ObjectOutput;->RatingCompat:Lcom/abox/apps/model/VoiceInfo;

    aput-object v2, v15, v1

    sget-object v1, Lo/ObjectOutput;->MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    const/16 v2, 0x10

    aput-object v1, v15, v2

    invoke-static {v15}, Lkotlin/collections/CollectionsKt;->arrayListOf([Ljava/lang/Object;)Ljava/util/ArrayList;

    move-result-object v1

    :goto_0
    if-nez p2, :cond_1

    sget-object v2, Lo/ObjectOutput;->MediaSessionCompat$Token:Lcom/abox/apps/model/VoiceInfo;

    invoke-virtual {v1, v0, v2}, Ljava/util/ArrayList;->add(ILjava/lang/Object;)V

    :cond_1
    if-eqz p1, :cond_2

    new-instance v2, Lcom/abox/apps/model/VoiceInfo;

    const/16 v18, -0x1

    sget v3, Lo/Cursor$FragmentManager;->onPanelClosed:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v20

    const/16 v21, 0x0

    const/16 v22, 0x0

    const/16 v23, 0x0

    const/16 v24, 0x0

    const/16 v25, 0x0

    const/16 v26, 0x0

    const/16 v27, 0x0

    const/16 v28, 0x0

    const/16 v29, 0x0

    const/16 v30, 0xff8

    const/16 v31, 0x0

    const-string v19, "More voices coming soon"

    move-object/from16 v17, v2

    invoke-direct/range {v17 .. v31}, Lcom/abox/apps/model/VoiceInfo;-><init>(ILjava/lang/String;Ljava/lang/Object;ILjava/lang/String;ZZZLcom/abox/audiotransform/VoiceConfig;Ljava/lang/Boolean;Ljava/lang/Boolean;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_2
    invoke-static {}, Lcom/tencent/mmkv/MMKV;->defaultMMKV()Lcom/tencent/mmkv/MMKV;

    move-result-object v2

    const/16 v3, 0x1e

    const-string v4, "default_select_id"

    invoke-virtual {v2, v4, v3}, Lcom/tencent/mmkv/MMKV;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/abox/apps/model/VoiceInfo;

    invoke-virtual {v4, v0}, Lcom/abox/apps/model/VoiceInfo;->setCheckedStatus(Z)V

    invoke-virtual {v4, v0}, Lcom/abox/apps/model/VoiceInfo;->setSelectedStatus(Z)V

    goto :goto_1

    :cond_3
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_6

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/abox/apps/model/VoiceInfo;

    invoke-virtual {v4}, Lcom/abox/apps/model/VoiceInfo;->getId()I

    move-result v5

    if-ne v2, v5, :cond_4

    move/from16 v5, v16

    goto :goto_3

    :cond_4
    move v5, v0

    :goto_3
    invoke-virtual {v4, v5}, Lcom/abox/apps/model/VoiceInfo;->setSelectedStatus(Z)V

    invoke-virtual {v4}, Lcom/abox/apps/model/VoiceInfo;->getId()I

    move-result v5

    if-ne v2, v5, :cond_5

    move/from16 v5, v16

    goto :goto_4

    :cond_5
    move v5, v0

    :goto_4
    invoke-virtual {v4, v5}, Lcom/abox/apps/model/VoiceInfo;->setCheckedStatus(Z)V

    goto :goto_2

    :cond_6
    if-eqz p2, :cond_9

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_7
    :goto_5
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_8

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    move-object v3, v2

    check-cast v3, Lcom/abox/apps/model/VoiceInfo;

    invoke-virtual {v3}, Lcom/abox/apps/model/VoiceInfo;->getShowInMainView()Z

    move-result v3

    if-eqz v3, :cond_7

    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_5

    :cond_8
    invoke-static {v0}, Lo/FileDescriptor;->asBinder(Ljava/util/List;)Ljava/util/ArrayList;

    move-result-object v1

    :cond_9
    return-object v1
.end method

.method public final getDefaultImpl()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->RemoteActionCompatParcelizer:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final getDefaultImpl(Lcom/abox/audiotransform/VoiceConfig;)V
    .locals 3
    .param p1    # Lcom/abox/audiotransform/VoiceConfig;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string/jumbo v0, "voiceConfig"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    const-string v1, "notify third app voice config changed"

    invoke-static {v1, v0}, Lo/eq;->asInterface(Ljava/lang/String;[Ljava/lang/Object;)V

    new-instance v0, Landroid/content/Intent;

    const-string v1, "change_voice_config_receiver_action"

    invoke-direct {v0, v1}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/abox/audiotransform/VoiceConfig;->getVoiceId()I

    move-result v1

    const-string/jumbo v2, "voice_id_key"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;I)Landroid/content/Intent;

    invoke-virtual {p1}, Lcom/abox/audiotransform/VoiceConfig;->getVoiceType()I

    move-result v1

    const-string/jumbo v2, "voice_type"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;I)Landroid/content/Intent;

    invoke-virtual {p1}, Lcom/abox/audiotransform/VoiceConfig;->getTempo()F

    move-result v1

    const-string/jumbo v2, "tempo_key"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;F)Landroid/content/Intent;

    invoke-virtual {p1}, Lcom/abox/audiotransform/VoiceConfig;->getPitchSemi()F

    move-result v1

    const-string v2, "pitch_semi_key"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;F)Landroid/content/Intent;

    invoke-virtual {p1}, Lcom/abox/audiotransform/VoiceConfig;->getRate()F

    move-result p1

    const-string v1, "rate_key"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;F)Landroid/content/Intent;

    invoke-static {}, Lo/Switch;->onTransact()Lo/Spinner;

    move-result-object p1

    invoke-interface {p1, v0}, Lo/Spinner;->asBinder(Landroid/content/Intent;)V

    return-void
.end method

.method public final onConnected()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaBrowserCompat$CustomActionResultReceiver:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final onConnectionFailed()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaDescriptionCompat:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final onConnectionSuspended()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaMetadataCompat:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final onTransact()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->read:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final read()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaBrowserCompat$ItemReceiver:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final setDefaultImpl()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->onConnected:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final setDefaultImpl(Lcom/abox/apps/model/VoiceInfo;Z)V
    .locals 4
    .param p1    # Lcom/abox/apps/model/VoiceInfo;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string/jumbo v0, "voiceInfo"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/abox/apps/model/VoiceInfo;->getId()I

    move-result v0

    invoke-virtual {p1}, Lcom/abox/apps/model/VoiceInfo;->getVoiceConfig()Lcom/abox/audiotransform/VoiceConfig;

    move-result-object v1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    new-array p1, v2, [Ljava/lang/Object;

    const-string/jumbo p2, "voiceConfig is null"

    invoke-static {p2, p1}, Lo/eq;->asInterface(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void

    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "do change voice selectedId:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-array v3, v2, [Ljava/lang/Object;

    invoke-static {v1, v3}, Lo/eq;->asInterface(Ljava/lang/String;[Ljava/lang/Object;)V

    invoke-static {}, Lcom/tencent/mmkv/MMKV;->defaultMMKV()Lcom/tencent/mmkv/MMKV;

    move-result-object v1

    const-string v3, "default_select_id"

    invoke-virtual {v1, v3, v0}, Lcom/tencent/mmkv/MMKV;->putInt(Ljava/lang/String;I)Landroid/content/SharedPreferences$Editor;

    invoke-virtual {p1}, Lcom/abox/apps/model/VoiceInfo;->getVoiceConfig()Lcom/abox/audiotransform/VoiceConfig;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lo/ObjectOutput;->getDefaultImpl(Lcom/abox/audiotransform/VoiceConfig;)V

    sget-object v0, Lo/FileNotFoundException;->asBinder:Lo/FileNotFoundException;

    invoke-virtual {v0}, Lo/FileNotFoundException;->read()V

    sget-object v0, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string v1, "changeVoiceHome"

    if-eqz p2, :cond_1

    move-object v3, v1

    goto :goto_0

    :cond_1
    const-string v3, "changeVoiceSuccess"

    :goto_0
    invoke-virtual {v0, v3}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object v0, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    if-eqz p2, :cond_2

    goto :goto_1

    :cond_2
    const-string v1, "changeVoice"

    :goto_1
    new-instance p2, Landroid/os/Bundle;

    invoke-direct {p2}, Landroid/os/Bundle;-><init>()V

    invoke-virtual {p1}, Lcom/abox/apps/model/VoiceInfo;->getVoiceConfig()Lcom/abox/audiotransform/VoiceConfig;

    move-result-object p1

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Lcom/abox/audiotransform/VoiceConfig;->getVoiceId()I

    move-result p1

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    const-string/jumbo v3, "voiceId"

    invoke-virtual {p2, v3, p1}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    invoke-virtual {v0, v1, p2}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;Landroid/os/Bundle;)V

    invoke-static {}, Lo/cw;->asInterface()Landroid/content/Context;

    move-result-object p1

    const-string p2, "getContext(...)"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    sget p2, Lo/Cursor$TaskStackBuilder;->ActivityViewModelLazyKt$viewModels$2:I

    const/4 v0, 0x2

    const/4 v1, 0x0

    invoke-static {p1, p2, v2, v0, v1}, Lo/FilePermission;->asInterface(Landroid/content/Context;IIILjava/lang/Object;)V

    return-void
.end method

.method public final setInternalConnectionCallback()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->MediaBrowserCompat$MediaItem:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method

.method public final write()Lcom/abox/apps/model/VoiceInfo;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    sget-object v0, Lo/ObjectOutput;->onConnectionFailed:Lcom/abox/apps/model/VoiceInfo;

    return-object v0
.end method
